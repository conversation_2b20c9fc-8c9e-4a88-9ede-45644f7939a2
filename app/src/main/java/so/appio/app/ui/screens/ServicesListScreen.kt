package so.appio.app.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Business
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MediumTopAppBar
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import so.appio.app.data.entity.service.Service
import so.appio.app.ui.theme.AppioAppTheme
import java.util.Date

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ServicesListScreen(
    modifier: Modifier = Modifier,
    services: List<Service>,
    onServiceClick: (String) -> Unit = {},
) {
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior()

    Scaffold(
        modifier = modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        topBar = {
            MediumTopAppBar(
                title = {
                    Text(
                        text = "Services",
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                scrollBehavior = scrollBehavior
            )
        }
    ) { innerPadding ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            verticalArrangement = Arrangement.spacedBy(0.dp)
        ) {
            items(services) { service ->
                ServiceListItem(
                    service = service,
                    onClick = { onServiceClick(service.id) }
                )
                HorizontalDivider()
            }
        }
    }
}

@Composable
private fun ServiceListItem(
    service: Service,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    ListItem(
        modifier = modifier,
        headlineContent = {
            Text(
                text = service.title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.titleMedium
            )
        },
        supportingContent = if (service.description != null) {
            {
                Text(
                    text = service.description,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        } else null,
        leadingContent = {
            Icon(
                imageVector = Icons.Default.Business,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
        },
        onClick = onClick
    )
}

@Preview(showBackground = true)
@Composable
fun ServicesListScreenPreview() {
    AppioAppTheme {
        ServicesListScreen(
            services = listOf(
                Service(
                    id = "svc_demo_1",
                    title = "Demo Service 1",
                    description = "This is the first demo service",
                    logoURL = "https://example.com/logo1.png",
                    lastUpdate = Date(),
                    lastSync = Date()
                ),
                Service(
                    id = "svc_demo_2",
                    title = "Demo Service 2",
                    description = "This is the second demo service with a longer description that might wrap to multiple lines",
                    logoURL = "https://example.com/logo2.png",
                    lastUpdate = Date(),
                    lastSync = Date()
                ),
                Service(
                    id = "svc_demo_3",
                    title = "Demo Service 3",
                    description = null,
                    logoURL = "https://example.com/logo3.png",
                    lastUpdate = Date(),
                    lastSync = Date()
                )
            )
        )
    }
}
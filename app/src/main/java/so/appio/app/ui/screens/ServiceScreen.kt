package so.appio.app.ui.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.AppBarRow
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.FlexibleBottomAppBar
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MediumTopAppBar
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import android.content.res.Configuration
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.material3.pulltorefresh.PullToRefreshState
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.ui.theme.AppioAppTheme
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@ExperimentalMaterial3Api
@Composable
fun ServiceScreen(
    modifier: Modifier = Modifier,
    service: Service,
    notifications: List<Notification>,
    onBackClick: (() -> Unit)? = null,
) {
    val context = LocalContext.current
    val scrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior() // (rememberTopAppBarState())
//    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior()
//    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior()

    // Pull-to-refresh state
    var isRefreshing by remember { mutableStateOf(false) }
    val pullToRefreshState = rememberPullToRefreshState()
    val coroutineScope = rememberCoroutineScope()

    // Refresh function
    val onRefresh: () -> Unit = {
        if (!isRefreshing) {
            coroutineScope.launch {
                isRefreshing = true
                // TODO: refresh Simulate refresh operation
                delay(2000) // Replace with your actual refresh logic
                println("Refresh completed")
                isRefreshing = false
            }
        }
    }

    Scaffold(
        modifier = Modifier.nestedScroll(scrollBehavior.nestedScrollConnection),
        topBar = {
            ServiceHeader(
                service = service,
                scrollBehavior = scrollBehavior,
                onRefresh = onRefresh,
            )
        },
        content = { innerPadding ->
            ServiceContent(
                notifications = notifications,
                modifier = Modifier.padding(innerPadding),
                isRefreshing = isRefreshing,
                onRefresh = onRefresh,
                pullToRefreshState = pullToRefreshState,
            )
        },
        bottomBar = {
            ServiceFooter(
                notificationCount = notifications.size,
            )
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ServiceHeader(
    modifier: Modifier = Modifier,
    service: Service,
    scrollBehavior: TopAppBarScrollBehavior,
    onRefresh: () -> Unit,
) {
    MediumTopAppBar(
        modifier = modifier,
        scrollBehavior = scrollBehavior,
        title = {
            Text(service.title, maxLines = 1, overflow = TextOverflow.Ellipsis)
        },
        navigationIcon = {
            IconButton(onClick = { /* doSomething() */ }) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back"
                )
            }
        },
        actions = {
            AppBarRow(
                maxItemCount = 1, // could be 3
                overflowIndicator = {
                    IconButton(onClick = { it.show() }) {
                        Icon(
                            imageVector = Icons.Filled.MoreVert,
                            contentDescription = "Localized description"
                        )
                    }
                }
            ) {
                clickableItem(
                    onClick = onRefresh, // Connect to the same refresh logic
                    icon = {
                        Icon(
                            imageVector = Icons.Filled.Refresh,
                            contentDescription = null
                        )
                    },
                    label = "Refresh"
                )

//                        clickableItem(
//                            onClick = {},
//                            icon = {
//                                Icon(imageVector = Icons.Filled.Edit, contentDescription = null)
//                            },
//                            label = "Edit"
//                        )
//
//                        clickableItem(
//                            onClick = {},
//                            icon = {
//                                Icon(imageVector = Icons.Outlined.Star, contentDescription = null)
//                            },
//                            label = "Favorite"
//                        )
//
//                        clickableItem(
//                            onClick = {},
//                            icon = {
//                                Icon(imageVector = Icons.Filled.Snooze, contentDescription = null)
//                            },
//                            label = "Alarm"
//                        )
//
//                        clickableItem(
//                            onClick = {},
//                            icon = {
//                                Icon(
//                                    imageVector = Icons.Outlined.MarkEmailUnread,
//                                    contentDescription = "Localized description"
//                                )
//                            },
//                            label = "Email"
//                        )
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun ServiceContent(
    modifier: Modifier = Modifier,
    notifications: List<Notification>,
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    pullToRefreshState: PullToRefreshState,
) {
    PullToRefreshBox(
        isRefreshing = isRefreshing,
        onRefresh = onRefresh,
        state = pullToRefreshState,
        modifier = modifier,
        indicator = {
            PullToRefreshDefaults.LoadingIndicator(
                state = pullToRefreshState,
                isRefreshing = isRefreshing,
                modifier = Modifier.align(Alignment.TopCenter)
            )
        }
    ) {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(notifications) { notification ->
                NotificationItem(notification = notification)
                HorizontalDivider()
            }
        }
    }
}

@Composable
private fun NotificationItem(
    notification: Notification,
    modifier: Modifier = Modifier
) {
    ListItem(
        modifier = modifier,
        headlineContent = {
            Text(
                text = notification.title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        },
        supportingContent = {
            Text(
                text = notification.body,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        },
        trailingContent = {
            Text(
                text = formatDate(notification.receivedAt),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun ServiceFooter(
    modifier: Modifier = Modifier,
    notificationCount: Int,
) {
    FlexibleBottomAppBar(
        modifier = modifier,
        content = {
            Box(
                modifier = Modifier.fillMaxWidth(),
//                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "$notificationCount notifications",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    )
}

private fun formatDate(date: Date): String {
    val formatter = SimpleDateFormat("MMM dd\nHH:mm", Locale.getDefault())
    return formatter.format(date)
}

// -------------------------------------------------------------------------------------------------

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
fun ServiceScreenPreview() {
    AppioAppTheme {
        ServiceScreen(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service for testing purposes",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            ),
            notifications = emptyList(),
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true)
@Composable
fun ServiceScreenPreviewModePreview() {
    AppioAppTheme {
        ServiceScreen(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service with preview mode enabled",
                logoURL = "https://example.com/logo.png",
                showPreview = true,
                lastUpdate = Date(),
                lastSync = Date()
            ),
            notifications = emptyList(),
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun ServiceScreenDarkPreview() {
    AppioAppTheme {
        ServiceScreen(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "Dark mode preview of the service screen",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            ),
            notifications = emptyList(),
        )
    }
}

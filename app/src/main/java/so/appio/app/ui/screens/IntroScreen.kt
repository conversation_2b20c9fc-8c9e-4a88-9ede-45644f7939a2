package so.appio.app.ui.screens

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import so.appio.app.R
import so.appio.app.ui.theme.AppioAppTheme
import androidx.compose.runtime.LaunchedEffect
import so.appio.app.utils.FeatureFlagManager

@Composable
fun IntroScreen(
    modifier: Modifier = Modifier,
    context: Context,
    onTryDemoService: () -> Unit,
    onContinueSetup: () -> Unit,
    onValidQRCodeURL: ((String) -> Unit)? = null,
) {
    var showQRScanScreen by remember { mutableStateOf(false) }

    if (showQRScanScreen) {
        QRScanScreen(
            onBackClick = {
                showQRScanScreen = false
            },
            onValidAppioUrl = { url ->
                showQRScanScreen = false
                onValidQRCodeURL?.invoke(url)
            }
        )
    } else {
        IntroContent(
            context = context,
            onTryDemoService = onTryDemoService,
            onContinueSetup = onContinueSetup,
            onScanQRCode = {
                showQRScanScreen = true
            },
            modifier = modifier
        )
    }
}

@Composable
private fun IntroContent(
    modifier: Modifier = Modifier,
    context: Context,
    onTryDemoService: () -> Unit,
    onContinueSetup: () -> Unit,
    onScanQRCode: () -> Unit,
) {
    var ffIntro by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        ffIntro = FeatureFlagManager(context).get<String>("intro") == "A---"
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(
                bottom = 24.dp + WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding(),
                start = 24.dp,
                end = 24.dp,
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Spacer(modifier = Modifier.weight(1f))
        
        // Illustration - Simple connection graphic
        Box(
            modifier = Modifier.padding(bottom = 32.dp),
            contentAlignment = Alignment.Center
        ) {
            // Connection illustration using the plug vector drawable
            Icon(
                painter = painterResource(id = R.drawable.ic_plug),
                contentDescription = "Illustration of connection",
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
        
        // Main title
        Text(
            text = buildAnnotatedString {
                append("Let's get you\n")
                withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                    append("Connected")
                }
            },
            style = MaterialTheme.typography.headlineLarge.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 32.sp,
                lineHeight = 40.sp
            ),
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        // Description text
        if (ffIntro) {
            Text(
                text = buildAnnotatedString {
                    append("To get started, connect to\na service that uses Appio.\nAppio is a companion app\nfor ")
                    withStyle(style = SpanStyle(color = MaterialTheme.colorScheme.primary)) {
                        append("https://appio.so")
                    }
                },
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontSize = 16.sp,
                    lineHeight = 24.sp
                ),
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.weight(.4f))

            // Primary button - Try demo service
            Button(
                onClick = onTryDemoService,
                elevation = ButtonDefaults.buttonElevation(
                    defaultElevation = 5.dp
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                )
            ) {
                Text(
                    text = "Try demo service",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp
                    )
                )
            }
        } else {
            Spacer(modifier = Modifier.weight(.4f))

            // Primary button - Continue setup
            Button(
                onClick = onContinueSetup,
                elevation = ButtonDefaults.buttonElevation(
                    defaultElevation = 5.dp
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                )
            ) {
                Text(
                    text = "Continue setup",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp
                    )
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "If you came from a browser",
                style = MaterialTheme.typography.bodySmall,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // "or" text with lines
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Left line
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(MaterialTheme.colorScheme.outlineVariant)
            )

            // "or" text
            Text(
                text = "or",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            // Right line
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(1.dp)
                    .background(MaterialTheme.colorScheme.outlineVariant)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
        
        // Secondary button - Scan QR code
        TextButton(
            onClick = onScanQRCode,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp)
                .height(56.dp),
        ) {
            Text(
                text = "Scan the QR code",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Medium,
                    fontSize = 16.sp
                ),
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun IntroScreenPreview() {
    AppioAppTheme {
        // Note: Preview won't show feature flag behavior since context is not available
        Column {
            Text("Preview - Feature flag logic requires real context")
        }
    }
}

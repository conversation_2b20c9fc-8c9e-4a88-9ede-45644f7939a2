package so.appio.app.ui.screens

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import so.appio.app.data.AppDataStore
import so.appio.app.utils.IntentHandler
import so.appio.app.utils.InstallReferrer
import so.appio.app.utils.StartupUrlProcessor
import so.appio.app.widgets.AppioWidget
import androidx.glance.appwidget.updateAll
import so.appio.app.data.entity.service.Service
import so.appio.app.utils.FingerPrintManager
import so.appio.app.utils.FeatureFlagManager
import so.appio.app.data.database.DatabaseManager

class MainViewModel : ViewModel() {

    companion object {
        private const val TAG = "LOG:MainViewModel"
    }

    private val _isSplashScreenLoading = MutableStateFlow(true)
    val isSplashScreenLoading: StateFlow<Boolean> = _isSplashScreenLoading

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading

    // Services state - single source of truth
    private val _services = MutableStateFlow<List<Service>>(emptyList())
    val services: StateFlow<List<Service>> = _services

    // Selected service state
    private val _selectedService = MutableStateFlow<Service?>(null)

    // Computed navigation states
    val showServicesList: StateFlow<Boolean> = combine(_services, _selectedService) { services, selected ->
        services.size > 1 && selected == null
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), false)

    val currentService: StateFlow<Service?> = combine(_services, _selectedService) { services, selected ->
        selected ?: if (services.size == 1) services.first() else null
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), null)

    // Dependencies
    private lateinit var appDataStore: AppDataStore
    private lateinit var appContext: Context
    private lateinit var fingerPrintManager: FingerPrintManager
    private lateinit var featureFlagManager: FeatureFlagManager
    private var isInitialized = false

    fun getContext(): Context {
        return appContext
    }

    fun initialize(context: Context) {
        try {
            appContext = context
            appDataStore = AppDataStore(context)
            fingerPrintManager = FingerPrintManager(context)
            featureFlagManager = FeatureFlagManager(context)
            isInitialized = true

            Log.d(TAG, "MainViewModel initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize MainViewModel", e)
            isInitialized = false
        }
    }

    /**
     * Load feature flags during splash screen initialization.
     * This ensures feature flags are available for all intent types.
     */
    fun loadFeatureFlags() {
        if (!ensureInitialized()) return

        viewModelScope.launch {
            try {
                Log.d(TAG, "Loading feature flags during splash screen...")
                featureFlagManager.check()
                Log.d(TAG, "Feature flags loaded successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading feature flags", e)
            } finally {
                // Dismiss splash screen after feature flags are loaded (or failed)
                Log.d(TAG, "Dismissing splash screen")
                _isSplashScreenLoading.value = false
            }
        }
    }



    /**
     * Check if the ViewModel is properly initialized
     * @return true if initialized, false otherwise
     */
    private fun ensureInitialized(): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "MainViewModel not initialized - call initialize() first")
            return false
        }
        return true
    }

    fun handleIntent(intent: Intent) {
        if (!ensureInitialized()) return

        val intentSource = IntentHandler.detectIntentSource(intent)
        Log.d(TAG, "Intent: $intentSource")

        // Start loading screen. Every intent has to cancel this
        _isLoading.value = true

        // Note: Splash screen will be dismissed by loadFeatureFlags() after feature flags are loaded
        // This ensures feature flags are available for all intent types

        // Delegate to appropriate intent handlers
        when (intentSource) {
            IntentHandler.IntentSource.NOTIFICATION -> IntentHandler.handleNotificationIntent(intent, this)
            IntentHandler.IntentSource.WIDGET -> IntentHandler.handleWidgetIntent(intent, this)
            IntentHandler.IntentSource.SHORTCUT -> IntentHandler.handleShortcutIntent(intent, this)
            IntentHandler.IntentSource.DEEP_LINK -> IntentHandler.handleDeepLinkIntent(intent, this)
            IntentHandler.IntentSource.NORMAL_LAUNCH -> {
                viewModelScope.launch {
                    Log.d(TAG, "Normal app launch")
                    detectServices()
                }
            }
            IntentHandler.IntentSource.OTHER -> IntentHandler.handleOtherIntent(intent, this)
        }
    }

    fun intentDone() {
        _isLoading.value = false
    }

    /**
     * Detect available services and set navigation state accordingly
     */
    private fun detectServices() {
        viewModelScope.launch {
            try {
                val serviceRepository = DatabaseManager.getServiceRepository()
                val servicesList = serviceRepository.getAllServicesList()
                _services.value = servicesList
                _selectedService.value = null // Reset selection

                Log.d(TAG, "Detected ${servicesList.size} services from database")
            } catch (e: Exception) {
                Log.e(TAG, "Error detecting services", e)
                _services.value = emptyList()
                _selectedService.value = null
            } finally {
                intentDone()
            }
        }
    }

    /**
     * Handle intent with specific service ID
     */
    fun handleServiceIntent(serviceId: String) {
        viewModelScope.launch {
            try {
                val serviceRepository = DatabaseManager.getServiceRepository()
                val service = serviceRepository.getServiceById(serviceId)

                if (service != null) {
                    Log.d(TAG, "Service found for ID $serviceId - showing ServiceScreen")
                    // Load all services first to populate the list, then select the specific service
                    val allServices = serviceRepository.getAllServicesList()
                    _services.value = allServices
                    _selectedService.value = service
                } else {
                    Log.e(TAG, "Service not found for ID: $serviceId - resuming standard flow")
                    detectServices()
                    return@launch
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading service with ID $serviceId", e)
                detectServices()
                return@launch
            } finally {
                intentDone()
            }
        }
    }

    /**
     * Show a specific service from the services list
     */
    fun showService(serviceId: String) {
        val service = _services.value.find { it.id == serviceId }
        if (service != null) {
            Log.d(TAG, "Showing service: ${service.title}")
            _selectedService.value = service
        } else {
            Log.e(TAG, "Service not found in loaded services: $serviceId")
        }
    }

    /**
     * Navigate back to services list (when multiple services exist)
     */
    fun navigateBackToServicesList() {
        if (_services.value.size > 1) {
            Log.d(TAG, "Navigating back to services list")
            _selectedService.value = null
        }
    }

    /**
     * Process startup URL using StartupUrlProcessor
     */
    fun processStartupUrl(url: String, done: () -> Unit) {
        viewModelScope.launch {
            try {
                val context = appContext
                val processor = StartupUrlProcessor.getInstance(context)
                val service = processor.processUrl(url)
                if (service != null) {
                    // Load all services first, then select the specific service
                    val serviceRepository = DatabaseManager.getServiceRepository()
                    val allServices = serviceRepository.getAllServicesList()
                    _services.value = allServices
                    _selectedService.value = service
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing startup URL", e)
            }
            done()
        }
    }

    /**
     * Handle install referrer processing
     */
    fun handleInstallReferrer(context: Context) {
        InstallReferrer.onValidUrl = { url ->
            Log.d(TAG, "Install referrer URL constructed: $url")
            processStartupUrl(url, this::intentDone)
        }
        InstallReferrer.detectInstallReferrer(context)
    }

    private fun refreshAllWidgets() {
        if (!ensureInitialized()) return

        Log.d(TAG, "Refreshing all widgets")

        viewModelScope.launch {
            try {
                AppioWidget().updateAll(appContext)
                Log.d(TAG, "All widgets updated")
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing widgets", e)
            }
        }
    }

    fun onStart() {
        Log.d(TAG, "onStart called")
        refreshAllWidgets()
    }

    fun onStop() {
        Log.d(TAG, "onStop")
        refreshAllWidgets()
    }






}
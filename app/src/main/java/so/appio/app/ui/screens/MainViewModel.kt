package so.appio.app.ui.screens

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import so.appio.app.data.AppDataStore
import so.appio.app.utils.IntentHandler
import so.appio.app.utils.InstallReferrer
import so.appio.app.utils.StartupUrlProcessor
import so.appio.app.widgets.AppioWidget
import androidx.glance.appwidget.updateAll
import so.appio.app.data.entity.service.Service
import so.appio.app.utils.FingerPrintManager
import so.appio.app.utils.FeatureFlagManager
import so.appio.app.data.database.DatabaseManager

class MainViewModel : ViewModel() {

    companion object {
        private const val TAG = "LOG:MainViewModel"
    }

    private val _isSplashScreenLoading = MutableStateFlow(true)
    val isSplashScreenLoading: StateFlow<Boolean> = _isSplashScreenLoading

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading

    private val _service = MutableStateFlow<Service?>(null)
    val service: StateFlow<Service?> = _service

    // Services list state
    private val _services = MutableStateFlow<List<Service>>(emptyList())
    val services: StateFlow<List<Service>> = _services

    // Navigation state
    private val _showServicesList = MutableStateFlow(false)
    val showServicesList: StateFlow<Boolean> = _showServicesList

    // Dependencies
    private lateinit var appDataStore: AppDataStore
    private lateinit var appContext: Context
    private lateinit var fingerPrintManager: FingerPrintManager
    private lateinit var featureFlagManager: FeatureFlagManager
    private var isInitialized = false

    fun getContext(): Context {
        return appContext
    }

    fun initialize(context: Context) {
        try {
            appContext = context
            appDataStore = AppDataStore(context)
            fingerPrintManager = FingerPrintManager(context)
            featureFlagManager = FeatureFlagManager(context)
            isInitialized = true

            Log.d(TAG, "MainViewModel initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize MainViewModel", e)
            isInitialized = false
        }
    }

    /**
     * Load feature flags during splash screen initialization.
     * This ensures feature flags are available for all intent types.
     */
    fun loadFeatureFlags() {
        if (!ensureInitialized()) return

        viewModelScope.launch {
            try {
                Log.d(TAG, "Loading feature flags during splash screen...")
                featureFlagManager.check()
                Log.d(TAG, "Feature flags loaded successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading feature flags", e)
            } finally {
                // Dismiss splash screen after feature flags are loaded (or failed)
                Log.d(TAG, "Dismissing splash screen")
                _isSplashScreenLoading.value = false
            }
        }
    }



    /**
     * Check if the ViewModel is properly initialized
     * @return true if initialized, false otherwise
     */
    private fun ensureInitialized(): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "MainViewModel not initialized - call initialize() first")
            return false
        }
        return true
    }

    fun handleIntent(intent: Intent) {
        if (!ensureInitialized()) return

        val intentSource = IntentHandler.detectIntentSource(intent)
        Log.d(TAG, "Intent: $intentSource")

        // Start loading screen. Every intent has to cancel this
        _isLoading.value = true

        // Check for service_id in intent extras for all intent types
        val serviceId = intent.extras?.getString("service_id")
        if (serviceId != null) {
            Log.d(TAG, "Service ID found in intent: $serviceId")
            handleServiceIntent(serviceId)
            return
        }

        // Note: Splash screen will be dismissed by loadFeatureFlags() after feature flags are loaded
        // This ensures feature flags are available for all intent types

        // Delegate to appropriate intent handlers
        when (intentSource) {
            IntentHandler.IntentSource.NOTIFICATION -> IntentHandler.handleNotificationIntent(intent, this)
            IntentHandler.IntentSource.WIDGET -> IntentHandler.handleWidgetIntent(intent, this)
            IntentHandler.IntentSource.SHORTCUT -> IntentHandler.handleShortcutIntent(intent, this)
            IntentHandler.IntentSource.DEEP_LINK -> IntentHandler.handleDeepLinkIntent(intent, this)
            IntentHandler.IntentSource.NORMAL_LAUNCH -> {
                viewModelScope.launch {
                    Log.d(TAG, "Normal app launch")
                    loadAllServices()
                }
            }
            IntentHandler.IntentSource.OTHER -> IntentHandler.handleOtherIntent(intent, this)
        }
    }

    fun intentDone() {
        _isLoading.value = false
    }

    /**
     * Load all services from database and determine navigation state
     */
    private fun loadAllServices() {
        viewModelScope.launch {
            try {
                val serviceRepository = DatabaseManager.getServiceRepository()
                val servicesList = serviceRepository.getAllServicesList()
                _services.value = servicesList

                Log.d(TAG, "Loaded ${servicesList.size} services from database")

                when (servicesList.size) {
                    0 -> {
                        Log.d(TAG, "No services found - showing IntroScreen")
                        _showServicesList.value = false
                        _service.value = null
                    }
                    1 -> {
                        Log.d(TAG, "Single service found - showing ServiceScreen directly")
                        _showServicesList.value = false
                        _service.value = servicesList.first()
                    }
                    else -> {
                        Log.d(TAG, "Multiple services found - showing ServicesListScreen")
                        _showServicesList.value = true
                        _service.value = null
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading services", e)
                _showServicesList.value = false
                _service.value = null
            } finally {
                intentDone()
            }
        }
    }

    /**
     * Handle intent with specific service ID
     */
    private fun handleServiceIntent(serviceId: String) {
        viewModelScope.launch {
            try {
                val serviceRepository = DatabaseManager.getServiceRepository()
                val service = serviceRepository.getServiceById(serviceId)

                if (service != null) {
                    Log.d(TAG, "Service found for ID $serviceId - showing ServiceScreen")
                    _service.value = service
                    _showServicesList.value = false
                } else {
                    Log.e(TAG, "Service not found for ID: $serviceId - resuming standard flow")
                    loadAllServices()
                    return@launch
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading service with ID $serviceId", e)
                loadAllServices()
                return@launch
            } finally {
                intentDone()
            }
        }
    }

    /**
     * Select a service from the services list
     */
    fun selectService(serviceId: String) {
        viewModelScope.launch {
            try {
                val service = _services.value.find { it.id == serviceId }
                if (service != null) {
                    Log.d(TAG, "Service selected: ${service.title}")
                    _service.value = service
                    _showServicesList.value = false
                } else {
                    Log.e(TAG, "Service not found in loaded services: $serviceId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error selecting service", e)
            }
        }
    }

    /**
     * Navigate back to services list (when multiple services exist)
     */
    fun navigateBackToServicesList() {
        if (_services.value.size > 1) {
            Log.d(TAG, "Navigating back to services list")
            _service.value = null
            _showServicesList.value = true
        }
    }

    /**
     * Process startup URL using StartupUrlProcessor
     */
    fun processStartupUrl(url: String, done: () -> Unit) {
        viewModelScope.launch {
            try {
                val context = appContext
                val processor = StartupUrlProcessor.getInstance(context)
                _service.value = processor.processUrl(url)
            } catch (e: Exception) {
                Log.e(TAG, "Error processing startup URL", e)
            }
            done()
        }
    }

    /**
     * Handle install referrer processing
     */
    fun handleInstallReferrer(context: Context) {
        InstallReferrer.onValidUrl = { url ->
            Log.d(TAG, "Install referrer URL constructed: $url")
            processStartupUrl(url, this::intentDone)
        }
        InstallReferrer.detectInstallReferrer(context)
    }

    private fun refreshAllWidgets() {
        if (!ensureInitialized()) return

        Log.d(TAG, "Refreshing all widgets")

        viewModelScope.launch {
            try {
                AppioWidget().updateAll(appContext)
                Log.d(TAG, "All widgets updated")
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing widgets", e)
            }
        }
    }

    fun onStart() {
        Log.d(TAG, "onStart called")
        refreshAllWidgets()
    }

    fun onStop() {
        Log.d(TAG, "onStop")
        refreshAllWidgets()
    }






}
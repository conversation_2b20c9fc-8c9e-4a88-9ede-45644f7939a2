package so.appio.app

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.activity.viewModels
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import so.appio.app.ui.screens.IntroScreen
import so.appio.app.ui.screens.LoadingScreen
import so.appio.app.ui.screens.MainViewModel
import so.appio.app.ui.screens.ServiceScreen
import so.appio.app.ui.screens.ServicesListScreen
import so.appio.app.ui.theme.AppioAppTheme
import so.appio.app.utils.BrowserUtils
import so.appio.app.utils.MySplashScreen
import so.appio.app.utils.NotificationPermissionManager
import so.appio.app.data.AppDataStore
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import so.appio.app.data.database.DatabaseManager

class MainActivity : ComponentActivity() {

    companion object {
        internal const val DEMO_URL = "https://demo.appio.so/?platform=android&app=true"
        internal const val APP_URL = "https://app.appio.so/?platform=android&app=true" // don't use /android? because that would open the app directly. we need to open browsed first to give it a chance to read cookies
        internal const val TAG = "LOG:MainActivity"
    }

    // ViewModel holds app's state and business logic
    private val viewModel: MainViewModel by viewModels()

    // Notification permission manager - MUST be initialized early in lifecycle
    private lateinit var notificationPermissionManager: NotificationPermissionManager

    // Public getter for UI components
    fun getNotificationPermissionManager(): NotificationPermissionManager = notificationPermissionManager

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        // Debug
        // DeviceInfoManager(this).logDeviceInfo()

        MySplashScreen.initialize(installSplashScreen(), viewModel)

        // Call after splash screen
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate called")

        // Init notification permission manager EARLY (before STARTED state)
        notificationPermissionManager = NotificationPermissionManager(this, AppDataStore(this))

        // Init other components
        viewModel.initialize(this)

        // Load feature flags during splash screen (blocking for all intent types)
        viewModel.loadFeatureFlags()

        // Handle intent and install referrer
        viewModel.handleIntent(intent)
        viewModel.handleInstallReferrer(this)

        enableEdgeToEdge()
        setContent {
            AppioAppTheme {
                val currentService by viewModel.currentService.collectAsState()
                val services by viewModel.services.collectAsState()
                val showServicesList by viewModel.showServicesList.collectAsState()
                val isLoading by viewModel.isLoading.collectAsState()

                when {
                    isLoading -> {
                        Log.d(TAG, "Showing LoadingScreen")
                        // Show loading screen while processing
                        LoadingScreen()
                    }
                    showServicesList -> {
                        Log.d(TAG, "Showing ServicesListScreen")
                        ServicesListScreen(
                            services = services,
                            onServiceClick = { service ->
                                Log.d(TAG, "Service selected from list: ${service.id}")
                                viewModel.navigateToService(service)
                            }
                        )
                    }
                    currentService != null -> {
                        Log.d(TAG, "Showing ServiceScreen")
                        val service = currentService!!
                        val notificationRepository = remember { DatabaseManager.getNotificationRepository() }
                        val notifications by notificationRepository.getNotificationsByService(service.id).collectAsState(initial = emptyList())

                        ServiceScreen(
                            service = service,
                            notifications = notifications,
                            onBackClick = if (services.size > 1) {
                                {
                                    Log.d(TAG, "Back button clicked - returning to services list")
                                    viewModel.navigateToAllServices()
                                }
                            } else null
                        )
                    }
                    else -> {
                        Log.d(TAG, "Showing IntroScreen")
                        // Show IntroScreen for NoAction
                        IntroScreen(
                            context = this@MainActivity,
                            onTryDemoService = {
                                BrowserUtils.openUrl(this@MainActivity, DEMO_URL)
                            },
                            onContinueSetup = {
                                BrowserUtils.openUrl(this@MainActivity, APP_URL)
                            },
                            onValidQRCodeURL = { url ->
                                Log.d(TAG, "Valid Appio URL from QR scan: $url")
                                viewModel.processStartupUrl(url, viewModel::intentDone)
                            }
                        )
                    }
                }
            }
        }
    }

    // Called if the Activity is already running and is launched again
    // (e.g., from another notification tap while the app is in the background but not killed)
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "onNewIntent called with intent: $intent")
        viewModel.handleIntent(intent)
    }

    // Called when the activity is becoming visible.
    override fun onStart() {
        super.onStart()
        Log.d(TAG, "onStart called")

        // Auto-request notification permissions
        lifecycleScope.launch {
            notificationPermissionManager.request()
        }

        // Call ViewModel onStart
        viewModel.onStart()
    }

    // Called when the activity is no longer visible. App is hidden, minimized, or another app is in the foreground.
    // App is still in memory and can be quickly resumed.
    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop")
        viewModel.onStop()
    }


}